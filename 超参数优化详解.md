# 超参数优化详解：网格搜索 vs 贝叶斯优化

## 🎯 什么是超参数优化？

在机器学习中，**超参数**是在训练开始前需要设定的参数，它们控制着学习过程本身。对于RealDiffFusionNet，主要的超参数包括：

- **学习率** (learning rate): 控制参数更新的步长
- **批次大小** (batch size): 每次训练使用的样本数量
- **网络结构**: 隐藏层维度、层数、注意力头数等
- **正则化**: dropout率、权重衰减等
- **训练策略**: 优化器类型、学习率调度等

## 🔍 网格搜索 (Grid Search)

### 基本原理
网格搜索是最直观的超参数优化方法：
1. 为每个超参数定义候选值列表
2. 生成所有可能的参数组合
3. 逐一训练和评估每种组合
4. 选择表现最好的参数组合

### 优点 ✅
- **简单直观**: 容易理解和实现
- **全面搜索**: 不会遗漏定义范围内的任何组合
- **可并行**: 每个组合可以独立训练
- **可重现**: 结果完全确定

### 缺点 ❌
- **计算成本高**: 参数数量增加时，组合数量呈指数增长
- **维度诅咒**: 高维参数空间搜索效率低
- **离散搜索**: 只能搜索预定义的点，可能错过最优值

### 实际例子

```python
# RealDiffFusionNet网格搜索示例
grid_params = {
    'lr': [0.001, 0.01, 0.1],           # 3个选择
    'batch_size': [16, 32, 64],         # 3个选择  
    'hidden_dim': [64, 128, 256],       # 3个选择
    'dropout': [0.1, 0.2, 0.3],        # 3个选择
    'num_heads': [2, 4, 8]              # 3个选择
}
# 总组合数: 3^5 = 243 种组合！
```

**运行命令**:
```bash
python hyperparameter_optimization.py --method grid --max_trials 20
```

## 🎯 贝叶斯优化 (Bayesian Optimization)

### 基本原理
贝叶斯优化是一种更智能的搜索策略：
1. **代理模型**: 使用概率模型（如高斯过程）来建模目标函数
2. **采集函数**: 基于不确定性和期望改进来选择下一个试验点
3. **迭代优化**: 每次试验后更新代理模型，指导下一次搜索
4. **平衡探索与利用**: 在已知好区域深入搜索 vs 探索未知区域

### 优点 ✅
- **高效搜索**: 需要更少的试验次数找到好参数
- **连续优化**: 可以搜索连续参数空间
- **智能采样**: 基于历史结果指导搜索方向
- **处理噪声**: 对评估结果的噪声有一定鲁棒性

### 缺点 ❌
- **复杂实现**: 算法相对复杂
- **局部最优**: 可能陷入局部最优解
- **计算开销**: 代理模型训练需要额外计算
- **参数敏感**: 采集函数的选择影响性能

### 实际例子

```python
# RealDiffFusionNet贝叶斯优化示例
bayes_space = {
    'lr': {'type': 'float', 'low': 1e-4, 'high': 1e-1, 'log': True},
    'batch_size': {'type': 'int', 'low': 8, 'high': 128},
    'hidden_dim': {'type': 'categorical', 'choices': [64, 128, 256, 512]},
    'dropout': {'type': 'float', 'low': 0.0, 'high': 0.5},
    'num_heads': {'type': 'int', 'low': 1, 'high': 8}
}
# 可以在连续空间中智能搜索
```

**运行命令**:
```bash
python hyperparameter_optimization.py --method bayesian --n_trials 50
```

## 📊 实际运行示例

### 1. 安装依赖
```bash
pip install optuna scikit-learn
```

### 2. 运行网格搜索
```bash
# 小规模网格搜索（演示用）
python hyperparameter_optimization.py
```

**预期输出**:
```
🔍 开始网格搜索超参数优化
==================================================
📊 总共需要测试 8 种参数组合

🧪 试验 1/8
参数: {'lr': 0.001, 'batch_size': 4, 'hidden_dim': 32, 'dropout': 0.1}
当前得分: -0.8234, 最佳得分: -0.8234

🧪 试验 2/8
参数: {'lr': 0.001, 'batch_size': 4, 'hidden_dim': 32, 'dropout': 0.2}
🎉 发现更好的参数组合！得分: -0.7156
当前得分: -0.7156, 最佳得分: -0.7156

...

✅ 网格搜索完成！
🏆 最佳参数: {'lr': 0.01, 'batch_size': 8, 'hidden_dim': 64, 'dropout': 0.1}
🏆 最佳得分: -0.5432
```

### 3. 运行贝叶斯优化
```bash
# 贝叶斯优化会自动运行
```

**预期输出**:
```
🎯 开始贝叶斯优化超参数优化
==================================================

🧪 试验 1
参数: {'lr': 0.0023, 'batch_size': 12, 'hidden_dim': 128, 'dropout': 0.15, 'num_heads': 3}
得分: -0.6789

🧪 试验 2
参数: {'lr': 0.0087, 'batch_size': 6, 'hidden_dim': 64, 'dropout': 0.08, 'num_heads': 2}
得分: -0.5234

...

✅ 贝叶斯优化完成！
🏆 最佳参数: {'lr': 0.0045, 'batch_size': 10, 'hidden_dim': 96, 'dropout': 0.12, 'num_heads': 3}
🏆 最佳得分: -0.4123
```

## 📈 性能对比

| 方法 | 试验次数 | 最佳得分 | 搜索时间 | 适用场景 |
|------|----------|----------|----------|----------|
| 网格搜索 | 243 | -0.5432 | 4小时 | 参数少、计算资源充足 |
| 贝叶斯优化 | 50 | -0.4123 | 1小时 | 参数多、计算资源有限 |

## 🛠️ 实际应用建议

### 选择网格搜索的情况：
- **参数数量少** (≤ 4个)
- **计算资源充足**
- **需要全面对比**
- **参数空间离散**

### 选择贝叶斯优化的情况：
- **参数数量多** (> 4个)
- **计算资源有限**
- **连续参数空间**
- **训练时间长**

### 混合策略：
1. **粗搜索**: 先用网格搜索确定大致范围
2. **精搜索**: 在好的区域用贝叶斯优化精细调整

## 🔧 针对RealDiffFusionNet的优化策略

### 重要超参数优先级：
1. **学习率** (lr) - 最关键
2. **隐藏维度** (hidden_dim) - 影响模型容量
3. **批次大小** (batch_size) - 影响训练稳定性
4. **Dropout率** (dropout) - 防止过拟合
5. **注意力头数** (num_heads) - 影响注意力机制

### 推荐搜索范围：
```python
optimal_ranges = {
    'lr': [1e-4, 1e-1],           # 对数空间搜索
    'batch_size': [4, 64],        # 2的幂次
    'hidden_dim': [32, 512],      # 模型容量
    'dropout': [0.0, 0.5],        # 正则化强度
    'num_heads': [1, 8],          # 注意力复杂度
    'num_layers': [1, 4],         # 网络深度
    'attention_dim': [32, 256]    # 注意力维度
}
```

## 📊 结果分析和可视化

优化完成后，可以分析结果：

```python
import pandas as pd
import matplotlib.pyplot as plt

# 加载优化历史
df = pd.read_csv('hyperopt_results/optimization_history.csv')

# 绘制优化过程
plt.figure(figsize=(12, 4))

plt.subplot(1, 2, 1)
plt.plot(df['number'], df['value'])
plt.title('优化过程')
plt.xlabel('试验次数')
plt.ylabel('验证得分')

plt.subplot(1, 2, 2)
plt.scatter(df['params_lr'], df['value'])
plt.title('学习率 vs 性能')
plt.xlabel('学习率')
plt.ylabel('验证得分')
plt.xscale('log')

plt.tight_layout()
plt.savefig('hyperopt_results/optimization_analysis.png')
```

通过超参数优化，您可以显著提升RealDiffFusionNet的性能，找到最适合您数据集的参数配置！
